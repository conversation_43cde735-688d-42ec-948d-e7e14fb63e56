describe('Navigation Links', () => {
  beforeEach(() => {
    cy.visit('http://localhost:3000/')
  })

  it('verifies footer links have correct href attributes', () => {
    // Test Footer Links
    // Home link
    cy.get('footer').contains('manystack').should('have.attr', 'href', '/')

    // Projects link
    cy.get('footer').contains('Our Projects').should('have.attr', 'href', '/projects')

    // Services link
    cy.get('footer').contains('Our Services').should('have.attr', 'href', '/services')

    // Privacy Promise link
    cy.get('footer')
      .contains('Privacy Promise')
      .should('have.attr', 'href', '/policies/privacy-promise')

    // Friendly Terms link
    cy.get('footer')
      .contains('Friendly Terms')
      .should('have.attr', 'href', '/policies/friendly-terms')
  })

  it('verifies social media links exist', () => {
    // Social media links (external)
    cy.get('footer a[href="https://facebook.com/mnystck"]').should('exist')
    cy.get('footer a[href="https://x.com/mnystck"]').should('exist')
    cy.get('footer a[href="https://www.linkedin.com/company/manystack"]').should('exist')
  })

  it('verifies content section links', () => {
    // Test Services Section Link
    cy.get('.grid-in-service-accordion')
      .contains('service page')
      .should('have.attr', 'href', '/services')
  })

  it('verifies navigation functionality works', () => {
    // Test that links actually work by visiting a few key ones
    cy.get('footer').contains('Our Services').click()
    cy.url().should('include', '/services')
    cy.go('back')

    cy.get('footer').contains('Our Projects').click()
    cy.url().should('include', '/projects')
    cy.go('back')
  })
})
