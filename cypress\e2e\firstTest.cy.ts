describe('manystack', () => {
  it('renders all the sections', () => {
    cy.visit('http://localhost:3000/')

    // Check that all the sections exist
    cy.get('.grid-in-hero').should('exist')
    cy.get('.grid-in-roadblocks').should('exist')
    cy.get('.grid-in-ascension-logs').should('exist')
    cy.get('.grid-in-application-development-intro').should('exist')
    cy.get('.grid-in-cards-plan').should('exist')
    cy.get('.grid-in-service-accordion').should('exist')
  })
  it('renders carousel section', () => {
    cy.visit('http://localhost:3000/')

    // Check that the carousel section exists and is visible
    cy.get('.grid-in-carousel').should('exist')

    // Check that all three carousel h2 titles exist within the carousel section
    cy.get('.grid-in-carousel h2').should('have.length', 3)
    cy.get('.grid-in-carousel h2').should('contain.text', 'What we love to do:')
    cy.get('.grid-in-carousel h2').should('contain.text', 'Building Dreams The Manystack Way')
    cy.get('.grid-in-carousel h2').should('contain.text', 'Dreams Already Crafted')
  })
  it('opens correct forms based on FormComponent prop logic', () => {
    cy.visit('http://localhost:3000/')

    // Test 1: ContactButton WITH FormComponent={EmailSubscriptionForm}
    // "Chart your concept!" (rainbow button) should open EmailSubscriptionForm
    cy.get('.grid-in-hero').contains('Chart your concept!').click()
    cy.get('[role="dialog"]').should('be.visible')
    cy.get('[role="dialog"]').should('contain.text', 'Need a Spark of Inspiration?')
    cy.get('[role="dialog"]').should('contain.text', 'Let me chart my concept!')
    cy.get('[role="dialog"] input[name="name"]').should('exist')
    cy.get('[role="dialog"] textarea[name="additionalNotes"]').should('exist')
    cy.get('[role="dialog"] input[name="email"]').should('exist')

    // Close dialog
    cy.get('body').type('{esc}')
    cy.get('[role="dialog"]').should('not.exist')

    // Test 2: ContactButton WITHOUT FormComponent (defaults to ContactForm)
    // "Drop a line to start!" (outline button) should open ContactForm
    cy.get('.grid-in-hero').contains('Drop a line to start!').click()
    cy.get('[role="dialog"]').should('be.visible')
    cy.get('[role="dialog"]').should('contain.text', "Let's Build It!")
    cy.get('[role="dialog"]').should('contain.text', 'Submit')
    cy.get('[role="dialog"] input[name="email"]').should('exist')
    cy.get('[role="dialog"] textarea[name="message"]').should('exist')
    cy.get('[role="dialog"] input[name="name"]').should('not.exist') // ContactForm doesn't have name field
    cy.get('[role="dialog"] textarea[name="additionalNotes"]').should('not.exist') // ContactForm doesn't have additionalNotes

    // Close dialog
    cy.get('body').type('{esc}')
    cy.get('[role="dialog"]').should('not.exist')

    // Test 3: ContactButton WITH FormComponent={ChallengeForm}
    // "Add my challenge!" should open ChallengeForm
    cy.get('.grid-in-roadblocks').contains('Add my challenge!').click()
    cy.get('[role="dialog"]').should('be.visible')
    cy.get('[role="dialog"]').should('contain.text', 'We Care About Your Toughest Challenges')
    cy.get('[role="dialog"]').should('contain.text', 'Help me tackle my challenge!')
    cy.get('[role="dialog"] input[name="name"]').should('exist')
    cy.get('[role="dialog"] input[name="email"]').should('exist')
    cy.get('[role="dialog"] textarea[name="yourChallenge"]').should('exist')
    cy.get('[role="dialog"] input[type="radio"]').should('exist') // Challenge options
    cy.get('[role="dialog"] textarea[name="message"]').should('not.exist') // ChallengeForm doesn't have message field

    // Close dialog
    cy.get('body').type('{esc}')
    cy.get('[role="dialog"]').should('not.exist')

    // Test 4: Footer ContactButton WITHOUT FormComponent (defaults to ContactForm)
    // "Your Story" should open ContactForm
    cy.get('footer').contains('Your Story').click()
    cy.get('[role="dialog"]').should('be.visible')
    cy.get('[role="dialog"]').should('contain.text', "Let's Build It!")
    cy.get('[role="dialog"]').should('contain.text', 'Submit')
    cy.get('[role="dialog"] input[name="email"]').should('exist')
    cy.get('[role="dialog"] textarea[name="message"]').should('exist')
    cy.get('[role="dialog"] input[name="name"]').should('not.exist') // ContactForm doesn't have name field

    // Close dialog
    cy.get('body').type('{esc}')
    cy.get('[role="dialog"]').should('not.exist')
  })

  it('verifies all navigation links are correct', () => {
    cy.visit('http://localhost:3000/')

    // Test Footer Links
    // Home link
    cy.get('footer').contains('manystack').should('have.attr', 'href', '/')

    // Projects link
    cy.get('footer').contains('Our Projects').should('have.attr', 'href', '/projects')

    // Services link
    cy.get('footer').contains('Our Services').should('have.attr', 'href', '/services')

    // Privacy Promise link
    cy.get('footer').contains('Privacy Promise').should('have.attr', 'href', '/policies/privacy-promise')

    // Friendly Terms link
    cy.get('footer').contains('Friendly Terms').should('have.attr', 'href', '/policies/friendly-terms')

    // Social media links (external)
    cy.get('footer a[href="https://facebook.com/mnystck"]').should('exist')
    cy.get('footer a[href="https://x.com/mnystck"]').should('exist')
    cy.get('footer a[href="https://www.linkedin.com/company/manystack"]').should('exist')

    // Test Services Section Link
    cy.get('.grid-in-service-accordion').contains('service page').should('have.attr', 'href', '/services')

    // Test that links actually work by visiting a few key ones
    cy.get('footer').contains('Our Services').click()
    cy.url().should('include', '/services')
    cy.go('back')

    cy.get('footer').contains('Our Projects').click()
    cy.url().should('include', '/projects')
    cy.go('back')
  })
})
