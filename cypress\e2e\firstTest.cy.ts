describe('manystack', () => {
  it('renders all the sections', () => {
    cy.visit('http://localhost:3000/')

    cy.get('.grid-in-hero').should('exist')
    cy.get('.grid-in-roadblocks').should('exist')
    cy.get('.grid-in-ascension-logs').should('exist')
    cy.get('.grid-in-application-development-intro').should('exist')
    cy.get('.grid-in-cards-plan').should('exist')
    cy.get('.grid-in-service-accordion').should('exist')
  })
  it('renders rightside carousel section', () => {
    cy.visit('http://localhost:3000/')

    cy.get('.grid-in-carousel').should('exist')
    // Get the service and feedback carousels via [&>*:first-child]:grid-in-service
    // Syntax error, unrecognized expression: [&>*:first-child]
    // Syntax error, unrecognized expression: [&>*:first-child]:grid-in-service
    // Regular grid-in-services dont exist because it is given to the first child of .grid-in-carousel
    //Timed out retrying after 4000ms: Expected to find element: .grid-in-service, but never found it.
    // TEST aria-roledescription="carousel" for "first" and "last" inside the carousel inside carousel Coloumn
    // Check for     grid-area: service; style
    cy.get('.grid-in-carousel').children().first().children().first().should('exist') //grid-in-service exists in the first child of the first child of .grid-in-carousel. This is the service carousel. It should exist.
    cy.get('.grid-in-carousel').children().first().children().last().should('exist') //grid-in-feedback exists in the last child of the first child of .grid-in-carousel. This is the feedback carousel. It should exist.
    cy.get('.grid-in-carousel').children().last().
  })
})
