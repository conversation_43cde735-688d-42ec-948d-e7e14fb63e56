describe('Contact Forms', () => {
  beforeEach(() => {
    cy.visit('http://localhost:3000/')
  })

  it('opens EmailSubscriptionForm when FormComponent is provided', () => {
    // Test ContactButton WITH FormComponent={EmailSubscriptionForm}
    // "Chart your concept!" (rainbow button) should open EmailSubscriptionForm
    cy.get('.grid-in-hero').contains('Chart your concept!').click()
    cy.get('[role="dialog"]').should('be.visible')
    cy.get('[role="dialog"]').should('contain.text', 'Need a Spark of Inspiration?')
    cy.get('[role="dialog"]').should('contain.text', 'Let me chart my concept!')
    cy.get('[role="dialog"] input[name="name"]').should('exist')
    cy.get('[role="dialog"] textarea[name="additionalNotes"]').should('exist')
    cy.get('[role="dialog"] input[name="email"]').should('exist')

    // Close dialog
    cy.get('body').type('{esc}')
    cy.get('[role="dialog"]').should('not.exist')
  })

  it('opens ContactForm when no FormComponent is provided (default)', () => {
    // Test ContactButton WITHOUT FormComponent (defaults to ContactForm)
    // "Drop a line to start!" (outline button) should open ContactForm
    cy.get('.grid-in-hero').contains('Drop a line to start!').click()
    cy.get('[role="dialog"]').should('be.visible')
    cy.get('[role="dialog"]').should('contain.text', "Let's Build It!")
    cy.get('[role="dialog"]').should('contain.text', 'Submit')
    cy.get('[role="dialog"] input[name="email"]').should('exist')
    cy.get('[role="dialog"] textarea[name="message"]').should('exist')
    cy.get('[role="dialog"] input[name="name"]').should('not.exist') // ContactForm doesn't have name field
    cy.get('[role="dialog"] textarea[name="additionalNotes"]').should('not.exist') // ContactForm doesn't have additionalNotes

    // Close dialog
    cy.get('body').type('{esc}')
    cy.get('[role="dialog"]').should('not.exist')
  })

  it('opens ChallengeForm when FormComponent is provided', () => {
    // Test ContactButton WITH FormComponent={ChallengeForm}
    // "Add my challenge!" should open ChallengeForm
    cy.get('.grid-in-roadblocks').contains('Add my challenge!').click()
    cy.get('[role="dialog"]').should('be.visible')
    cy.get('[role="dialog"]').should('contain.text', 'We Care About Your Toughest Challenges')
    cy.get('[role="dialog"]').should('contain.text', 'Help me tackle my challenge!')
    cy.get('[role="dialog"] input[name="name"]').should('exist')
    cy.get('[role="dialog"] input[name="email"]').should('exist')
    cy.get('[role="dialog"] textarea[name="yourChallenge"]').should('exist')
    cy.get('[role="dialog"] input[type="radio"]').should('exist') // Challenge options
    cy.get('[role="dialog"] textarea[name="message"]').should('not.exist') // ChallengeForm doesn't have message field

    // Close dialog
    cy.get('body').type('{esc}')
    cy.get('[role="dialog"]').should('not.exist')
  })

  it('opens ContactForm for footer CTA without FormComponent', () => {
    // Test Footer ContactButton WITHOUT FormComponent (defaults to ContactForm)
    // "Your Story" should open ContactForm
    cy.get('footer').contains('Your Story').click()
    cy.get('[role="dialog"]').should('be.visible')
    cy.get('[role="dialog"]').should('contain.text', "Let's Build It!")
    cy.get('[role="dialog"]').should('contain.text', 'Submit')
    cy.get('[role="dialog"] input[name="email"]').should('exist')
    cy.get('[role="dialog"] textarea[name="message"]').should('exist')
    cy.get('[role="dialog"] input[name="name"]').should('not.exist') // ContactForm doesn't have name field

    // Close dialog
    cy.get('body').type('{esc}')
    cy.get('[role="dialog"]').should('not.exist')
  })
})
